package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.SearchProductGuiController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.SaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.ToSaleGuiRelationDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SaleGuiService;
import javafx.application.Platform;

import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.util.retry.Retry;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SaleGuiController extends BaseController {

    private final SaleGuiService saleGuiService;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;

    @FXML
    private AnchorPane anchorDetalles;
    @FXML
    private AnchorPane anchorSale;
    @FXML
    private Button btnAddSearchProduct;
    @FXML
    private Label lblIniciadaPor;
    @FXML
    private Label lblusersConnected;
    @FXML
    private ProgressIndicator loadingIndicator;
    @FXML
    private SplitPane splitDetallesDividerX;
    @FXML
    private SplitPane splitSaleDividerY;
    @FXML
    private StackPane stackPaneSale;
    @FXML
    private TabPane tabPaneSearchProduct;

    // Datos iniciales
    private ToSaleGuiRelationDto currentRelation;
    private UUID saleGuiId; // ID del SaleGui (tipo UUID)
    private UUID currentSaleId; // Para rastrear si el saleId ha cambiado (tipo UUID según SaleGuiDto.saleId)
    private SaleController currentSaleController; // Referencia al controlador actual

    // Mapa: searchProductId -> Tab para evitar crear duplicados
    private final ConcurrentHashMap<UUID, Tab> searchProductTabs = new ConcurrentHashMap<>();
    // Registro previo de IDs para comparar en la siguiente actualización
    private Set<UUID> oldSearchProductIds = new HashSet<>();

    private Disposable subscription;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        btnAddSearchProduct.setOnAction(e -> handleAddSearchProduct());

        // Agregar un simple listener para el movimiento del divisor del SplitPane
        // Esto ayudará a mantener la visibilidad de los elementos seleccionados
        splitSaleDividerY.getDividers().get(0).positionProperty().addListener((obs, oldVal, newVal) -> {
            // Cuando el divisor termina de moverse, forzar la actualización de los ListViews
            if (currentSaleController != null) {
                Platform.runLater(() -> {
                    // Forzar actualización de los ListViews en SaleController
                    // Only refresh bienServicioCargados as bienServicioDevueltos has been removed
                    if (currentSaleController.getBienServicioCargados() != null) {
                        currentSaleController.getBienServicioCargados().refresh();
                    }
                });
            }
        });
    }

    public void setToSaleGuiRelation(ToSaleGuiRelationDto rel) {
        this.currentRelation = rel;
        this.saleGuiId = rel.getSaleGuiId();
        if (rel.getDividerX() != null) {
            splitDetallesDividerX.setDividerPositions(rel.getDividerX());
        }
        if (rel.getDividerY() != null) {
            splitSaleDividerY.setDividerPositions(rel.getDividerY());
        }
        subscribeToSaleGuiChanges();
    }

    private void subscribeToSaleGuiChanges() {
        if (subscription != null) {
            subscription.dispose();
        }
        subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                saleGuiService.subscribeToChanges(saleGuiId)
                        .retryWhen(Retry.backoff(3, Duration.ofMillis(200))
                                .filter(err -> err.getMessage() != null
                                        && err.getMessage().toLowerCase().contains("deadlock"))
                                .doBeforeRetry(signal ->
                                        log.warn("Reintentando suscripción a SaleGuiDto (intento #{}) por: {}",
                                                signal.totalRetries() + 1, signal.failure().getMessage()))
                        ),
                this::onSaleGuiUpdate,
                this::handleSubscriptionError,
                () -> log.debug("[CLIENT] Suscripción a SaleGuiDto completada.")
        );
        registerSubscription(subscription);
    }

    /**
     * Maneja las actualizaciones recibidas del servidor para el SaleGuiDto.
     * Optimizado para minimizar la recreación de componentes UI.
     */
    private void onSaleGuiUpdate(SaleGuiDto dto) {
        runOnUiThread(() -> {
            // Actualizar labels solo si han cambiado
            updateLabelsIfChanged(dto);

            // Manejar cambios en el Sale asociado
            handleSaleChanges(dto);

            // Actualizar Tabs de SearchProduct (método ya optimizado)
            updateSearchProductTabs(dto.getSearchProductIds());
        });
    }

    /**
     * Actualiza las etiquetas de información solo si los datos han cambiado.
     * Ahora obtiene la información de usuarios conectados desde el DTO.
     */
    private void updateLabelsIfChanged(SaleGuiDto dto) {
        // Actualizar iniciadaPor solo si ha cambiado
        if (!Objects.equals(lblIniciadaPor.getText(), dto.getIniciadaPor())) {
            lblIniciadaPor.setText(dto.getIniciadaPor());
        }

        // Actualizar usuarios conectados solo si han cambiado
        String newOnlineUsers = "";
        if (dto.getUsersConnected() != null) {
            newOnlineUsers = dto.getUsersConnected().stream()
                    .filter(user -> Boolean.TRUE.equals(user.getIsOnline()))
                    .map(SaleGuiDto.ConnectedUserDto::getUsername)
                    .collect(Collectors.joining(", "));
        }

        if (!Objects.equals(lblusersConnected.getText(), newOnlineUsers)) {
            lblusersConnected.setText(newOnlineUsers);
        }
    }

    /**
     * Maneja los cambios en el Sale asociado al SaleGui.
     */
    private void handleSaleChanges(SaleGuiDto dto) {
        // Verificar si el saleId ha cambiado o si es la primera carga
        boolean saleIdChanged = (currentSaleId == null && dto.getSaleId() != null) ||
                               (currentSaleId != null && !currentSaleId.equals(dto.getSaleId()));

        // Cargar la vista de Sale solo si el saleId ha cambiado o si no existe un controlador actual
        if (dto.getSaleId() != null && (saleIdChanged || currentSaleController == null)) {
            log.info("[CLIENT] Cargando nueva vista de Sale para saleId={} (anterior={})",
                    dto.getSaleId(), currentSaleId);

            // Actualizar el saleId actual
            currentSaleId = dto.getSaleId();

            // Cargar la nueva vista
            Parent saleView = springFXMLLoader.load(FXMLEnum.SALE);
            currentSaleController = (SaleController) springFXMLLoader.getController(saleView);
            currentSaleController.setSaleId(dto.getSaleId());

            // Actualizar la UI
            anchorSale.getChildren().clear();
            anchorSale.getChildren().add(saleView);
            AnchorPane.setTopAnchor(saleView, 0.0);
            AnchorPane.setBottomAnchor(saleView, 0.0);
            AnchorPane.setLeftAnchor(saleView, 0.0);
            AnchorPane.setRightAnchor(saleView, 0.0);

            // Actualizar la referencia al SaleController en todos los SearchProductGuiController existentes
            updateSaleControllerReferences();
        } else if (dto.getSaleId() != null && currentSaleController != null && !saleIdChanged) {
            // El saleId no ha cambiado, pero podría haber actualizaciones en el modelo
            // No es necesario hacer nada aquí, ya que el SaleController tiene su propia suscripción
            log.debug("[CLIENT] No se recarga la vista de Sale, saleId no ha cambiado: {}", dto.getSaleId());
        } else if (dto.getSaleId() == null && currentSaleId != null) {
            // El saleId se ha eliminado
            log.info("[CLIENT] Eliminando vista de Sale, saleId es nulo");
            currentSaleId = null;
            currentSaleController = null;
            anchorSale.getChildren().clear();
        }
    }

    /**
     * Actualiza la referencia al SaleController en todos los SearchProductGuiController existentes.
     * Optimizado para proporcionar más información de depuración y evitar operaciones innecesarias.
     */
    private void updateSaleControllerReferences() {
        if (currentSaleController == null) {
            log.debug("[CLIENT] No hay SaleController para actualizar referencias");
            return;
        }

        int updatedCount = 0;
        for (Tab tab : tabPaneSearchProduct.getTabs()) {
            // Necesitamos hacer un cast a Parent ya que tab.getContent() devuelve Node
            Parent tabContent = (Parent) tab.getContent();
            if (tabContent != null) {
                SearchProductGuiController spController = (SearchProductGuiController) springFXMLLoader.getController(tabContent);
                if (spController != null) {
                    UUID spId = (UUID) tab.getUserData();
                    log.debug("[CLIENT] Actualizando referencia a SaleController en SearchProductGuiController para tab {}", spId);
                    spController.setSaleController(currentSaleController);
                    updatedCount++;
                }
            }
        }

        log.debug("[CLIENT] Actualizadas {} referencias a SaleController en SearchProductGuiControllers", updatedCount);
    }

    /**
     * Actualiza (crea/elimina) los tabs de SearchProductGui en función de la lista proveniente del servidor.
     * Además, reordena y actualiza los títulos de cada tab para que muestren "Busqueda " seguido de la posición.
     * Optimizado para minimizar la recreación de tabs y componentes.
     */
    private void updateSearchProductTabs(List<UUID> newList) {
        if (newList == null) {
            newList = Collections.emptyList();
        }

        // Crear un conjunto para búsquedas más eficientes
        Set<UUID> newSet = new HashSet<>(newList);

        // 1) Si NO hay cambios en el conjunto de IDs, solo actualizamos los títulos si es necesario
        if (newSet.equals(oldSearchProductIds)) {
            // Verificar si el orden ha cambiado
            boolean orderChanged = false;
            List<UUID> currentOrder = tabPaneSearchProduct.getTabs().stream()
                    .map(tab -> (UUID) tab.getUserData())
                    .collect(Collectors.toList());

            if (!currentOrder.equals(newList)) {
                log.debug("[CLIENT] Orden de searchProductIds cambiado, actualizando títulos");
                orderChanged = true;
            }

            // Si el orden no ha cambiado, no hacemos nada
            if (!orderChanged) {
                log.debug("[CLIENT] No hay cambios en searchProductIds ni en su orden => se ignora update.");
                return;
            }

            // Si solo cambió el orden, actualizamos los títulos
            List<Tab> currentTabs = tabPaneSearchProduct.getTabs();
            for (int i = 0; i < currentTabs.size(); i++) {
                currentTabs.get(i).setText("Busqueda " + (i + 1));
            }
            return;
        }

        log.info("[CLIENT] searchProductIds cambiado => antes={} | ahora={}", oldSearchProductIds, newSet);

        // Identificar qué tabs se deben eliminar y cuáles agregar
        Set<UUID> toRemove = new HashSet<>(oldSearchProductIds);
        toRemove.removeAll(newSet); // IDs que estaban antes pero ya no están

        Set<UUID> toAdd = new HashSet<>(newSet);
        toAdd.removeAll(oldSearchProductIds); // IDs nuevos que no estaban antes

        // Actualizar el conjunto de IDs para la próxima comparación
        oldSearchProductIds = newSet;

        // 2) Remover tabs que ya no existen en la nueva lista (más eficiente que removeIf)
        if (!toRemove.isEmpty()) {
            log.debug("[CLIENT] Removiendo tabs: {}", toRemove);
            List<Tab> tabsToRemove = new ArrayList<>();
            for (Tab tab : tabPaneSearchProduct.getTabs()) {
                UUID spId = (UUID) tab.getUserData();
                if (toRemove.contains(spId)) {
                    tabsToRemove.add(tab);
                    searchProductTabs.remove(spId);
                }
            }
            tabPaneSearchProduct.getTabs().removeAll(tabsToRemove);
        }

        // 3) Crear tabs nuevos para IDs que no teníamos
        if (!toAdd.isEmpty()) {
            log.debug("[CLIENT] Agregando nuevos tabs: {}", toAdd);
            for (UUID spId : newList) { // Usamos newList para mantener el orden
                if (toAdd.contains(spId)) {
                    Tab tab = createSearchProductTab(spId);
                    searchProductTabs.put(spId, tab);
                    tabPaneSearchProduct.getTabs().add(tab);
                }
            }
        }

        // 4) Reordenar tabs según el orden de la lista (si es necesario)
        if (!toAdd.isEmpty() || !toRemove.isEmpty() || !tabPaneSearchProduct.getTabs().stream()
                .map(tab -> (UUID) tab.getUserData())
                .collect(Collectors.toList())
                .equals(newList)) {

            log.debug("[CLIENT] Reordenando tabs según el orden del servidor");

            // Crear un mapa de los tabs actuales para acceso rápido
            Map<UUID, Tab> currentTabs = new HashMap<>();
            for (Tab tab : tabPaneSearchProduct.getTabs()) {
                UUID spId = (UUID) tab.getUserData();
                currentTabs.put(spId, tab);
            }

            // Crear lista con el nuevo orden
            List<Tab> newOrder = new ArrayList<>();
            for (UUID spId : newList) {
                Tab tab = currentTabs.get(spId);
                if (tab != null) {
                    newOrder.add(tab);
                }
            }

            // Actualizar el orden de los tabs
            tabPaneSearchProduct.getTabs().setAll(newOrder);
        }

        // 5) Actualizar títulos de todos los tabs según su posición (posición 1-based)
        List<Tab> currentTabs = tabPaneSearchProduct.getTabs();
        for (int i = 0; i < currentTabs.size(); i++) {
            currentTabs.get(i).setText("Busqueda " + (i + 1));
        }
    }

    /**
     * Crea un nuevo Tab para un SearchProductGui dado su UUID.
     */
    private Tab createSearchProductTab(UUID spId) {
        Tab tab = new Tab();
        Parent spView = springFXMLLoader.load(FXMLEnum.SEARCH_PRODUCT);
        SearchProductGuiController spController = springFXMLLoader.getController(spView);
        spController.setSearchProductGuiId(spId);

        // Usar la referencia al SaleController actual si existe
        if (currentSaleController != null) {
            log.debug("Conectando SearchProductGuiController con SaleController existente");
            spController.setSaleController(currentSaleController);
        } else {
            log.debug("No hay SaleController disponible para conectar con SearchProductGuiController");
        }

        tab.setContent(spView);
        tab.setClosable(true);
        tab.setUserData(spId);

        // Al cerrar el tab, se solicita al servidor eliminar el SearchProductGui
        tab.setOnCloseRequest(event -> {
            subscribeOnBoundedElastic(
                    saleGuiService.deleteSearchProduct(saleGuiId, spId),
                    unused -> log.debug("[CLIENT] SearchProductGui {} eliminado OK", spId),
                    error -> {
                        log.error("[CLIENT] Error al eliminar SearchProductGui {}", spId, error);
                        runOnUiThread(() -> {
                            alertUtil.showError("Error al eliminar SearchProduct: " + error.getMessage());
                            event.consume(); // Evita el cierre si falla
                        });
                    }
            );
        });
        return tab;
    }

    /**
     * Botón para crear un nuevo SearchProductGui en el SaleGui.
     */
    private void handleAddSearchProduct() {
        if (saleGuiId == null) {
            alertUtil.showError("SaleGuiId no configurado.");
            return;
        }
        subscribeOnBoundedElastic(
                saleGuiService.createSearchProduct(saleGuiId),
                unused -> log.debug("[CLIENT] Nuevo SearchProductGui creado."),
                error -> {
                    log.error("[CLIENT] Error al crear SearchProductGui", error);
                    runOnUiThread(() ->
                            alertUtil.showError("No se pudo agregar SearchProduct: " + error.getMessage())
                    );
                }
        );
    }

    private void handleSubscriptionError(Throwable error) {
        log.error("[CLIENT] Error en la suscripción de SaleGuiDto: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de SaleGui: " + error.getMessage())
        );
    }

    @Override
    public void onClose() {
        super.onClose();
        if (subscription != null && !subscription.isDisposed()) {
            subscription.dispose();
        }
        log.info("[CLIENT] SaleGuiController cerrado.");
    }
}
