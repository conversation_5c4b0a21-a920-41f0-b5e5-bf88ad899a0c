package corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui;

import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.MainSaleGuiDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class MainSaleGuiService {

    private static final String ROUTE_GET_BY_USER       = "mainSaleGui.getByUser";
    private static final String ROUTE_SUBSCRIBE         = "mainSaleGui.subscribe";
    private static final String ROUTE_CREATE_SALE_GUI   = "mainSaleGui.createSaleGui";
    private static final String ROUTE_ADD_SALE_GUI      = "mainSaleGui.addSaleGui";
    private static final String ROUTE_REMOVE_SALE_GUI   = "mainSaleGui.removeSaleGui";
    private static final String ROUTE_REORDER_SALE_GUI  = "mainSaleGui.reorder";

    private final ConnectionService connectionService;

    /**
     * Obtiene el MainSaleGuiDto de un usuario por su username.
     */
    public Mono<MainSaleGuiDto> getMainSaleGuiOfUser(String username) {
        log.debug("Solicitando MainSaleGuiDto para el usuario: {}", username);
        return connectionService.authenticatedRequest(ROUTE_GET_BY_USER, username, MainSaleGuiDto.class);
    }

    /**
     * Se suscribe a los cambios en el MainSaleGuiDto identificado por su UUID.
     */
    public Flux<MainSaleGuiDto> subscribeToChanges(UUID mainSaleGuiId) {
        log.debug("Suscribiéndose a cambios de MainSaleGuiDto con id: {}", mainSaleGuiId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, mainSaleGuiId, MainSaleGuiDto.class);
    }

    /**
     * Crea un nuevo SaleGui en el MainSaleGui identificado.
     */
    public Mono<Void> createNewSaleGui(UUID mainSaleGuiId) {
        log.debug("Creando nuevo SaleGui en MainSaleGui con id: {}", mainSaleGuiId);
        return connectionService.authenticatedRequest(ROUTE_CREATE_SALE_GUI, mainSaleGuiId, Void.class);
    }

    /**
     * Agrega un SaleGui existente (por id) al MainSaleGui.
     */
    public Mono<Void> addSaleGui(UUID mainSaleGuiId, UUID saleGuiId) {
        log.debug("Agregando SaleGui con id={} a MainSaleGui con id={}", saleGuiId, mainSaleGuiId);
        var request = new AddSaleGuiRequest(mainSaleGuiId, saleGuiId);
        return connectionService.authenticatedRequest(ROUTE_ADD_SALE_GUI, request, Void.class);
    }

    /**
     * Remueve un SaleGui del MainSaleGui.
     */
    public Mono<Void> removeSaleGui(UUID mainSaleGuiId, UUID saleGuiId) {
        log.debug("Removiendo SaleGui con id={} de MainSaleGui con id={}", saleGuiId, mainSaleGuiId);
        var request = new RemoveSaleGuiRequest(mainSaleGuiId, saleGuiId);
        return connectionService.authenticatedRequest(ROUTE_REMOVE_SALE_GUI, request, Void.class);
    }

    /**
     * Reordena un SaleGui en el MainSaleGui a una nueva posición.
     */
    public Mono<Void> reorderSaleGui(UUID mainSaleGuiId, UUID saleGuiId, int newPosition) {
        log.debug("Reordenando SaleGui con id={} en MainSaleGui con id={} a la posición={}",
                saleGuiId, mainSaleGuiId, newPosition);
        var request = new ReorderSaleGuiRequest(mainSaleGuiId, saleGuiId, newPosition);
        return connectionService.authenticatedRequest(ROUTE_REORDER_SALE_GUI, request, Void.class);
    }

    // Records para agrupar los parámetros en las solicitudes
    public record AddSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId) {}
    public record RemoveSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId) {}
    public record ReorderSaleGuiRequest(UUID mainSaleGuiId, UUID saleGuiId, int newPosition) {}
}
