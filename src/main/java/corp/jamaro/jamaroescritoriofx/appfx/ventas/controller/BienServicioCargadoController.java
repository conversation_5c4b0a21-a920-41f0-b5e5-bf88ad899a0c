package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.SaleService;
import javafx.fxml.FXML;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Controlador para la vista de un BienServicioCargado en el ListView de SaleController.
 * Cada celda del ListView representa un item cargado en la venta.
 * Versión simplificada sin funcionalidad de expansión.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class BienServicioCargadoController extends BaseController {

    private final SaleService saleService;
    private final AlertUtil alertUtil;

    // Componentes FXML - Labels permanentes con nombres originales
    @FXML
    private AnchorPane anchorPrecioCantidad;

    @FXML
    private Label lblCodCompuesto;

    @FXML
    private Label descripcionDelBienServicio;

    @FXML
    private StackPane stackDescripcion;

    @FXML
    private Label lblGrupoItem;

    @FXML
    private Label lblMarca;

    @FXML
    private Label montoAcordado;

    @FXML
    private Label precioAcordado;

    @FXML
    private Label cantidad;

    @FXML
    private AnchorPane rootBienServicioCargado;

    @FXML
    private VBox vbDatosItem;

    // TextFields overlay para edición temporal
    @FXML
    private TextField txtDescripcionEdit;

    @FXML
    private TextField txtPrecioEdit;

    @FXML
    private TextField txtCantidadEdit;

    @FXML
    private TextField txtMontoEdit;

    // Estado y propiedades
    private ContextMenu contextMenu;
    private NumberFormat decimalFormat;
    private DecimalFormat validationFormat;
    private BienServicioCargado bienServicioCargado;
    private UUID saleId;

    // Patrones de validación
    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d*\\.?\\d{0,2}$");
    private static final Pattern INTEGER_PATTERN = Pattern.compile("^\\d+$");

    /**
     * Inicializa los formatos decimales para mostrar y validar precios.
     */
    private void initializeDecimalFormat() {
        decimalFormat = NumberFormat.getNumberInstance();
        decimalFormat.setMinimumFractionDigits(2);
        decimalFormat.setMaximumFractionDigits(2);

        validationFormat = new DecimalFormat("#.##");
        validationFormat.setMaximumFractionDigits(2);
    }

    /**
     * Configura el controlador con un BienServicioCargado y actualiza la UI.
     * @param bienServicioCargado El BienServicioCargado a mostrar
     * @param saleId ID de la venta para las actualizaciones
     */
    public void setBienServicioCargado(BienServicioCargado bienServicioCargado, UUID saleId) {
        this.bienServicioCargado = bienServicioCargado;
        this.saleId = saleId;
        updateUI();
    }

    /**
     * Actualiza la interfaz de usuario con los datos del BienServicioCargado.
     */
    private void updateUI() {
        if (bienServicioCargado == null) {
            log.warn("No se puede actualizar UI: bienServicioCargado es null");
            return;
        }

        // Aplicar estilo según el stock
        applyStockStyling();

        // Mostrar código compuesto
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getCodCompuesto() != null) {
            lblCodCompuesto.setText(bienServicioCargado.getItem().getCodCompuesto());
        } else {
            lblCodCompuesto.setText("N/A");
        }

        // Mostrar marca
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getMarca() != null) {
            lblMarca.setText(bienServicioCargado.getItem().getMarca().getNombre());
        } else {
            lblMarca.setText("");
        }

        // Mostrar grupo del item
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getProductos() != null) {
            // Intentar obtener el grupo del primer producto asociado al item
            bienServicioCargado.getItem().getProductos().stream()
                    .findFirst()
                    .ifPresent(producto -> {
                        Set<Grupo> grupos = producto.getGrupos();
                        if (grupos != null && !grupos.isEmpty()) {
                            grupos.stream().findFirst().ifPresent(grupo ->
                                lblGrupoItem.setText(grupo.getNombrePrincipal())
                            );
                        } else {
                            lblGrupoItem.setText("");
                        }
                    });
        } else {
            lblGrupoItem.setText("");
        }

        // Mostrar descripción en Camel Case - usar descripcionDelBienServicio si está disponible
        String descripcionToShow = "";
        if (bienServicioCargado.getDescripcionDelBienServicio() != null && !bienServicioCargado.getDescripcionDelBienServicio().trim().isEmpty()) {
            descripcionToShow = bienServicioCargado.getDescripcionDelBienServicio();
        } else if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getDescripcion() != null) {
            descripcionToShow = bienServicioCargado.getItem().getDescripcion();
        }
        String descripcionFormateada = toCamelCase(descripcionToShow);
        descripcionDelBienServicio.setText(descripcionFormateada);

        // Mostrar precio acordado sin símbolo de moneda
        if (bienServicioCargado.getPrecioAcordado() != null) {
            precioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioAcordado()));
        } else if (bienServicioCargado.getPrecioInicial() != null) {
            // Si no hay precio acordado, usar el precio inicial
            precioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioInicial()));
        } else {
            precioAcordado.setText(decimalFormat.format(0));
        }

        // Mostrar cantidad con ceros adelante (formato 3 dígitos)
        if (bienServicioCargado.getCantidad() != null) {
            int cantidadValue = bienServicioCargado.getCantidad().intValue();
            cantidad.setText(String.format("%03d", cantidadValue));
        } else {
            cantidad.setText("000");
        }

        // Mostrar total (cantidad x precio) sin símbolo de moneda
        if (bienServicioCargado.getMontoAcordado() != null) {
            montoAcordado.setText(decimalFormat.format(bienServicioCargado.getMontoAcordado()));
        } else {
            // Calcular el total si no está disponible
            double precio = bienServicioCargado.getPrecioAcordado() != null ?
                    bienServicioCargado.getPrecioAcordado() :
                    (bienServicioCargado.getPrecioInicial() != null ? bienServicioCargado.getPrecioInicial() : 0);
            double cantidadValue = bienServicioCargado.getCantidad() != null ? bienServicioCargado.getCantidad() : 0;
            montoAcordado.setText(decimalFormat.format(precio * cantidadValue));
        }
    }


    /**
     * Aplica estilos según el nivel de stock del item.
     * Items con stock cero o negativo tendrán un fondo de color de error.
     */
    private void applyStockStyling() {
        // Limpiar estilos previos
        rootBienServicioCargado.getStyleClass().removeAll("stock-error");

        // Verificar si el item tiene stock cero o negativo
        if (bienServicioCargado.getItem() != null &&
            bienServicioCargado.getItem().getStockTotal() != null &&
            bienServicioCargado.getItem().getStockTotal() <= 0) {
            // Aplicar estilo de error para stock cero o negativo
            rootBienServicioCargado.getStyleClass().add("stock-error");
        }
    }

    /**
     * Maneja el clic en el elemento para mostrar menú contextual.
     */
    @FXML
    private void handleClick(MouseEvent event) {
        if (event.getButton() == MouseButton.SECONDARY) {
            // Clic derecho: mostrar menú contextual
            showContextMenu(event);
            event.consume();
        }
    }



    /**
     * Muestra el menú contextual con opciones para el BienServicioCargado.
     */
    private void showContextMenu(MouseEvent event) {
        if (contextMenu == null) {
            createContextMenu();
        }

        // Mostrar el menú en la posición del clic
        contextMenu.show(rootBienServicioCargado, event.getScreenX(), event.getScreenY());
    }

    /**
     * Crea el menú contextual con las opciones disponibles.
     */
    private void createContextMenu() {
        contextMenu = new ContextMenu();
        contextMenu.getStyleClass().add("context-menu");

        // Opción para eliminar el BienServicioCargado
        MenuItem deleteItem = new MenuItem("Eliminar");
        deleteItem.getStyleClass().add("delete-menu-item");
        deleteItem.setOnAction(event -> deleteBienServicioCargado());

        contextMenu.getItems().add(deleteItem);
    }

    /**
     * Elimina el BienServicioCargado actual de la venta.
     */
    private void deleteBienServicioCargado() {
        if (saleId == null || bienServicioCargado == null || bienServicioCargado.getId() == null) {
            log.warn("No se puede eliminar BienServicioCargado: datos insuficientes");
            return;
        }

        log.debug("Eliminando BienServicioCargado {} de la venta {}",
                 bienServicioCargado.getId(), saleId);

        // Llamar al servicio para eliminar
        saleService.deleteBienServicioCargado(saleId, bienServicioCargado.getId())
                .subscribe(
                        response -> {
                            if (response.success()) {
                                log.info("BienServicioCargado eliminado exitosamente: {}",
                                        bienServicioCargado.getId());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos
                                // una actualización a través de la suscripción en SaleController
                            } else {
                                log.warn("Error al eliminar BienServicioCargado: {}", response.message());
                                runOnUiThread(() ->
                                    alertUtil.showError("Error al eliminar el item: " + response.message())
                                );
                            }
                        },
                        error -> {
                            log.error("Error al eliminar BienServicioCargado: {}", error.getMessage(), error);
                            runOnUiThread(() ->
                                alertUtil.showError("Error al eliminar el item: " + error.getMessage())
                            );
                        }
                );
    }



    /**
     * Convierte un texto a formato Camel Case.
     * Ejemplo: "RODAJE DEL PROBOX" -> "Rodaje Del Probox"
     *
     * @param text El texto a convertir
     * @return El texto en formato Camel Case
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // Dividir el texto en palabras
        String[] words = text.toLowerCase().split("\\s+");
        StringBuilder camelCase = new StringBuilder();

        // Convertir cada palabra a Camel Case
        for (String word : words) {
            if (!word.isEmpty()) {
                camelCase.append(Character.toUpperCase(word.charAt(0)))
                         .append(word.substring(1))
                         .append(" ");
            }
        }

        // Eliminar el espacio final y devolver el resultado
        return camelCase.toString().trim();
    }

    /**
     * Método de inicialización llamado por JavaFX después de que todos los elementos @FXML
     * han sido inyectados. Implementación requerida por la interfaz Initializable.
     *
     * @param url La ubicación utilizada para resolver rutas relativas para el objeto raíz
     * @param resourceBundle Los recursos utilizados para localizar el objeto raíz
     */
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicializar el formato decimal
        initializeDecimalFormat();

        // Configurar campos editables con overlay strategy
        configureEditableOverlays();

        log.debug("BienServicioCargadoController inicializado");
    }

    /**
     * Configura todos los campos editables usando la estrategia de overlay.
     * Labels permanentes + TextFields temporales que aparecen al hacer doble clic.
     */
    private void configureEditableOverlays() {
        // Configurar descripción (acepta cualquier String)
        configureStringField(descripcionDelBienServicio, txtDescripcionEdit, this::updateDescripcion);

        // Configurar precio acordado (acepta Double con máximo 2 decimales)
        configureDecimalField(precioAcordado, txtPrecioEdit, this::updatePrecioAcordado);

        // Configurar cantidad (acepta Double con máximo 2 decimales)
        configureDecimalField(cantidad, txtCantidadEdit, this::updateCantidad);

        // Configurar monto acordado (acepta Double con máximo 2 decimales)
        configureDecimalField(montoAcordado, txtMontoEdit, this::updateMontoAcordado);
    }

    /**
     * Configura un campo de texto (String) con overlay de edición.
     */
    private void configureStringField(Label label, TextField overlay, Runnable onSave) {
        // Configurar doble clic en el label
        label.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                startStringEditing(label, overlay);
                event.consume();
            }
        });

        // Configurar eventos del overlay
        setupOverlayEvents(overlay, () -> finishStringEditing(label, overlay, onSave), () -> cancelEditing(label, overlay));
    }

    /**
     * Configura un campo decimal (Double) con overlay de edición y validación.
     */
    private void configureDecimalField(Label label, TextField overlay, Runnable onSave) {
        // Configurar doble clic en el label
        label.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                startDecimalEditing(label, overlay);
                event.consume();
            }
        });

        // Configurar validación en tiempo real
        setupDecimalValidation(overlay);

        // Configurar eventos del overlay
        setupOverlayEvents(overlay, () -> finishDecimalEditing(label, overlay, onSave), () -> cancelEditing(label, overlay));
    }

    /**
     * Configura los eventos básicos del overlay (Enter, Escape, pérdida de foco).
     */
    private void setupOverlayEvents(TextField overlay, Runnable onSave, Runnable onCancel) {
        // Confirmar con Enter
        overlay.setOnKeyPressed(event -> {
            if (event.getCode() == KeyCode.ENTER) {
                onSave.run();
                event.consume();
            } else if (event.getCode() == KeyCode.ESCAPE) {
                onCancel.run();
                event.consume();
            }
        });

        // Confirmar al perder el foco
        overlay.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal && overlay.isVisible()) {
                onSave.run();
            }
        });
    }

    /**
     * Configura validación en tiempo real para campos decimales.
     */
    private void setupDecimalValidation(TextField overlay) {
        overlay.textProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null && !newVal.isEmpty() && !DECIMAL_PATTERN.matcher(newVal).matches()) {
                overlay.setText(oldVal); // Revertir a valor anterior si no es válido
            }
        });
    }

    // Métodos de inicio de edición

    /**
     * Inicia la edición de un campo de texto.
     */
    private void startStringEditing(Label label, TextField overlay) {
        overlay.setText(label.getText());
        label.setVisible(false);
        overlay.setVisible(true);
        overlay.requestFocus();
        overlay.selectAll();
    }

    /**
     * Inicia la edición de un campo decimal.
     */
    private void startDecimalEditing(Label label, TextField overlay) {
        // Convertir el texto formateado a número para edición
        String labelText = label.getText().replace(",", "");
        overlay.setText(labelText);
        label.setVisible(false);
        overlay.setVisible(true);
        overlay.requestFocus();
        overlay.selectAll();
    }

    // Métodos de finalización de edición

    /**
     * Finaliza la edición de un campo de texto.
     */
    private void finishStringEditing(Label label, TextField overlay, Runnable onSave) {
        label.setText(overlay.getText());
        overlay.setVisible(false);
        label.setVisible(true);
        onSave.run();
    }

    /**
     * Finaliza la edición de un campo decimal.
     */
    private void finishDecimalEditing(Label label, TextField overlay, Runnable onSave) {
        try {
            // Validar que el texto sea un número válido
            String text = overlay.getText().trim();
            if (!text.isEmpty()) {
                Double.parseDouble(text); // Validar formato
                label.setText(text); // Actualizar label con valor sin formato
                overlay.setVisible(false);
                label.setVisible(true);
                onSave.run();
            } else {
                cancelEditing(label, overlay);
            }
        } catch (NumberFormatException e) {
            log.warn("Formato decimal inválido: {}", overlay.getText());
            cancelEditing(label, overlay);
        }
    }

    /**
     * Cancela la edición y restaura el estado original.
     */
    private void cancelEditing(Label label, TextField overlay) {
        overlay.setVisible(false);
        label.setVisible(true);
        updateUI(); // Restaurar valores originales
    }

    // Métodos de actualización usando SaleService

    /**
     * Actualiza la descripción del bien servicio.
     */
    private void updateDescripcion() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar descripción: datos insuficientes");
            return;
        }

        String nuevaDescripcion = descripcionDelBienServicio.getText();

        saleService.updateBienServicioCargado(
                saleId,
                bienServicioCargado.getId(),
                bienServicioCargado.getPrecioAcordado(),
                bienServicioCargado.getCantidad(),
                nuevaDescripcion
        ).subscribe(
                response -> {
                    if (response.success()) {
                        log.debug("Descripción actualizada exitosamente");
                    } else {
                        log.warn("Error al actualizar descripción: {}", response.message());
                        runOnUiThread(this::updateUI);
                    }
                },
                error -> {
                    log.error("Error al actualizar descripción: {}", error.getMessage());
                    runOnUiThread(this::updateUI);
                }
        );
    }

    /**
     * Actualiza el precio acordado.
     */
    private void updatePrecioAcordado() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar precio acordado: datos insuficientes");
            return;
        }

        try {
            String precioText = precioAcordado.getText().replace(",", "");
            Double nuevoPrecio = Double.parseDouble(precioText);

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    nuevoPrecio,
                    bienServicioCargado.getCantidad(),
                    descripcionDelBienServicio.getText()
            ).subscribe(
                    response -> {
                        if (response.success()) {
                            log.debug("Precio acordado actualizado exitosamente");
                            // Reformatear el label con el formato decimal
                            runOnUiThread(() -> precioAcordado.setText(decimalFormat.format(nuevoPrecio)));
                        } else {
                            log.warn("Error al actualizar precio acordado: {}", response.message());
                            runOnUiThread(this::updateUI);
                        }
                    },
                    error -> {
                        log.error("Error al actualizar precio acordado: {}", error.getMessage());
                        runOnUiThread(this::updateUI);
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de precio inválido: {}", precioAcordado.getText());
            updateUI();
        }
    }

    /**
     * Actualiza la cantidad.
     */
    private void updateCantidad() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar cantidad: datos insuficientes");
            return;
        }

        try {
            String cantidadText = cantidad.getText().replace(",", "");
            Double nuevaCantidad = Double.parseDouble(cantidadText);

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    bienServicioCargado.getPrecioAcordado(),
                    nuevaCantidad,
                    descripcionDelBienServicio.getText()
            ).subscribe(
                    response -> {
                        if (response.success()) {
                            log.debug("Cantidad actualizada exitosamente");
                            // Reformatear el label con formato de 3 dígitos
                            runOnUiThread(() -> cantidad.setText(String.format("%03d", nuevaCantidad.intValue())));
                        } else {
                            log.warn("Error al actualizar cantidad: {}", response.message());
                            runOnUiThread(this::updateUI);
                        }
                    },
                    error -> {
                        log.error("Error al actualizar cantidad: {}", error.getMessage());
                        runOnUiThread(this::updateUI);
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de cantidad inválido: {}", cantidad.getText());
            updateUI();
        }
    }

    /**
     * Actualiza el monto acordado y calcula automáticamente el precio acordado.
     */
    private void updateMontoAcordado() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar monto acordado: datos insuficientes");
            return;
        }

        try {
            String montoText = montoAcordado.getText().replace(",", "");
            Double nuevoMonto = Double.parseDouble(montoText);
            Double cantidadActual = bienServicioCargado.getCantidad();

            if (cantidadActual == null || cantidadActual <= 0) {
                log.warn("No se puede calcular precio acordado: cantidad es cero o nula");
                updateUI();
                return;
            }

            // Calcular el nuevo precio acordado dividiendo el monto entre la cantidad
            Double nuevoPrecioAcordado = nuevoMonto / cantidadActual;

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    nuevoPrecioAcordado,
                    cantidadActual,
                    descripcionDelBienServicio.getText()
            ).subscribe(
                    response -> {
                        if (response.success()) {
                            log.debug("Monto acordado actualizado exitosamente");
                            // Reformatear el label con el formato decimal
                            runOnUiThread(() -> montoAcordado.setText(decimalFormat.format(nuevoMonto)));
                        } else {
                            log.warn("Error al actualizar monto acordado: {}", response.message());
                            runOnUiThread(this::updateUI);
                        }
                    },
                    error -> {
                        log.error("Error al actualizar monto acordado: {}", error.getMessage());
                        runOnUiThread(this::updateUI);
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de monto inválido: {}", montoAcordado.getText());
            updateUI();
        }
    }

}
