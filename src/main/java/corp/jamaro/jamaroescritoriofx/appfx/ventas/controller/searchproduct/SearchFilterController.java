package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.NombreGrupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.GrupoService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.filter.FilterStringController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.components.FXLabelUtil;
import javafx.animation.PauseTransition;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Alert;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.MenuItem;
import javafx.scene.control.PopupControl;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Tooltip;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseButton;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.util.Duration;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.kordamp.ikonli.javafx.FontIcon;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SearchFilterController extends BaseController {

    // Using custom setter method instead of Lombok-generated one
    private SearchProductGui searchProductGui; // Modelo que representa la configuración de búsqueda

    // Referencia al controlador padre para forzar recargas de productos
    @Setter
    private SearchProductGuiController parentController;

    private final GrupoService grupoService;
    private final SearchProductGuiService searchProductGuiService;
    private final SpringFXMLLoader springFXMLLoader;

    // Controles de la UI
    @FXML private HBox grupos;
    @FXML private HBox comodines;
    @FXML private ScrollPane scrollPaneGrupoFiltros;
    @FXML private VBox grupoFiltros;
    @FXML private ProgressIndicator loadingIndicator;
    @FXML private CustomTextField txtSearch;
    @FXML private CustomTextField txtVehiculo;

    // Variables para evitar actualizaciones innecesarias
    private String lastGroupId = null;
    private String lastVehiculo = null;
    private boolean isUpdatingVehiculoField = false;

    // Mapas para gestionar componentes de filtro (para evitar recargar el FXML repetidamente)
    private final Map<String, FilterStringController> filterControllerMap = new TreeMap<>();
    private final Map<String, Parent> filterParentMap = new TreeMap<>();

    // Variables para el modo comando
    private boolean isCommandMode = false;
    private CommandType currentCommandType = null;
    private final Label commandIndicatorLabel = new Label();
    private final PauseTransition debounceTransition = new PauseTransition(Duration.millis(400));
    // Nuevo PauseTransition para txtVehiculo
    private final PauseTransition vehiculoDebounceTransition = new PauseTransition(Duration.millis(400));

    // Binding para el autocompletado
    private AutoCompletionBinding<Object> autoCompletionBinding;

    // Enum para distinguir los tipos de comando
    private enum CommandType {
        DESCRIPCION,
        CODIGO_PRODUCTO,
        CODIGO_FABRICA
    }

    @Override
    public void initialize(URL url, java.util.ResourceBundle resourceBundle) {
        configureAutoCompletion();         // Configura las sugerencias en txtSearch
        configureVehiculoFieldListener();  // Configura el listener del campo vehiculo
        configureCommandModeKeyEvents();   // Configura eventos de teclado para el modo comando
        configureFilterRowContextMenu();   // Configura el menú contextual para duplicar filas de filtros
    }

    // ─── CONFIGURACIÓN DEL AUTOCOMPLETADO ─────────────────────────────
    private void configureAutoCompletion() {
        autoCompletionBinding = TextFields.bindAutoCompletion(txtSearch, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) return Collections.emptyList();

            if (!isCommandMode && userText.startsWith("/")) {
                return List.of("Descripción", "Código de Producto", "Código de Fábrica");
            }
            if (isCommandMode && currentCommandType == CommandType.DESCRIPCION) {
                searchProductGui.setDescripcion(userText);
                List<?> suggestions = searchProductGuiService.productoDescripcionSugerencias(searchProductGui)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .block(java.time.Duration.ofSeconds(2));
                return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
            }
            if (isCommandMode && (currentCommandType == CommandType.CODIGO_PRODUCTO ||
                    currentCommandType == CommandType.CODIGO_FABRICA)) {
                return Collections.emptyList();
            }
            List<?> suggestions = grupoService.searchNombresGrupo(userText)
                    .collectList()
                    .onErrorReturn(Collections.emptyList())
                    .block(java.time.Duration.ofSeconds(2));
            return suggestions != null ? new ArrayList<>(suggestions) : Collections.emptyList();
        });

        PopupControl popup = autoCompletionBinding.getAutoCompletionPopup();
        popup.setConsumeAutoHidingEvents(false);
        popup.showingProperty().addListener((obs, wasShowing, isShowing) -> {
            if (isShowing) {
                ListView<?> listView = (ListView<?>) popup.getSkin().getNode();
                if (listView != null) {
                    listView.prefWidthProperty().bind(txtSearch.widthProperty());
                    listView.maxWidthProperty().bind(txtSearch.widthProperty());
                }
                popup.setAnchorX(txtSearch.localToScreen(txtSearch.getBoundsInLocal()).getMinX());
                popup.setAnchorY(txtSearch.localToScreen(txtSearch.getBoundsInLocal()).getMaxY() + 5);
            }
        });

        autoCompletionBinding.setOnAutoCompleted(event -> {
            Object completion = event.getCompletion();
            if (isCommandMode && currentCommandType == CommandType.DESCRIPCION) {
                if (completion instanceof String suggestion && !suggestion.equals("Descripción")) {
                    confirmCommand(suggestion);
                    return;
                }
            }
            if (completion instanceof String selected) {
                if (selected.equals("Descripción") ||
                        selected.equals("Código de Producto") ||
                        selected.equals("Código de Fábrica")) {
                    isCommandMode = true;
                    currentCommandType = switch (selected) {
                        case "Descripción" -> CommandType.DESCRIPCION;
                        case "Código de Producto" -> CommandType.CODIGO_PRODUCTO;
                        default -> CommandType.CODIGO_FABRICA;
                    };
                    commandIndicatorLabel.setText(selected + ": ");
                    txtSearch.setLeft(commandIndicatorLabel);
                    String currentValue = switch (currentCommandType) {
                        case DESCRIPCION -> searchProductGui.getDescripcion();
                        case CODIGO_PRODUCTO -> searchProductGui.getCodProductoOld();
                        case CODIGO_FABRICA -> searchProductGui.getCodFabricaOld();
                    };
                    txtSearch.setText(currentValue != null ? currentValue : "");
                    runOnUiThread(() -> txtSearch.selectAll());
                    return;
                }
            }
            if (completion instanceof NombreGrupo selectedNombreGrupo) {
                log.info("Sugerencia de grupo seleccionada: {}", selectedNombreGrupo);
                if (searchProductGui != null && selectedNombreGrupo.getId() != null) {
                    subscribeOnBoundedElastic(
                            grupoService.getGrupoByNombreGrupoId(selectedNombreGrupo.getId())
                                    .flatMap(grupo ->
                                            searchProductGuiService.updateGrupo(searchProductGui.getId(), grupo.getId())
                                    ),
                            updatedGui -> log.info("Grupo actualizado en SearchProductGui"),
                            logError("actualizando Grupo")
                    );
                    runOnUiThread(() -> txtSearch.clear());
                }
            }
        });
    }

    // ─── CONFIGURACIÓN DE EVENTOS PARA EL MODO COMANDO ─────────────────────────────
    private void configureCommandModeKeyEvents() {
        txtSearch.setOnKeyPressed(event -> {
            if (isCommandMode && currentCommandType != null) {
                if (event.getCode() == KeyCode.ENTER) {
                    debounceTransition.stop();
                    confirmCommand(txtSearch.getText().trim());
                } else if (event.getCode() == KeyCode.ESCAPE) {
                    if (currentCommandType == CommandType.CODIGO_PRODUCTO) {
                        searchProductGui.setCodProductoOld(null);
                        updateComodinesServiceCall("cancelando Código de Producto",
                                gui -> runOnUiThread(this::updateComodinesLabels));
                    } else if (currentCommandType == CommandType.CODIGO_FABRICA) {
                        searchProductGui.setCodFabricaOld(null);
                        updateComodinesServiceCall("cancelando Código de Fábrica",
                                gui -> runOnUiThread(this::updateComodinesLabels));
                    }
                    cancelCommand();
                    txtSearch.clear();
                }
            }
        });

        txtSearch.textProperty().addListener((obs, oldText, newText) -> {
            if (!isCommandMode && newText.startsWith("/") && newText.length() > 1) {
                isCommandMode = true;
                currentCommandType = CommandType.DESCRIPCION;
                commandIndicatorLabel.setText("Descripción: ");
                txtSearch.setLeft(commandIndicatorLabel);
                String trimmed = newText.substring(1);
                runOnUiThread(() -> {
                    txtSearch.setText(trimmed);
                    txtSearch.positionCaret(trimmed.length());
                });
                return;
            }
            if (isCommandMode) {
                if (currentCommandType == CommandType.DESCRIPCION) {
                    searchProductGui.setDescripcion(newText);
                    debounceFieldUpdate(newText, CommandType.DESCRIPCION);
                    return;
                } else if (currentCommandType == CommandType.CODIGO_PRODUCTO ||
                        currentCommandType == CommandType.CODIGO_FABRICA) {
                    debounceFieldUpdate(newText, currentCommandType);
                }
            }
        });
    }

    /**
     * Método auxiliar para implementar el debounce en la actualización de campos txtSearch.
     */
    private void debounceFieldUpdate(String newText, CommandType commandType) {
        debounceTransition.stop();
        debounceTransition.setOnFinished(e -> {
            switch (commandType) {
                case DESCRIPCION -> searchProductGui.setDescripcion(newText);
                case CODIGO_PRODUCTO -> searchProductGui.setCodProductoOld(newText);
                case CODIGO_FABRICA -> searchProductGui.setCodFabricaOld(newText);
            }
            updateComodinesServiceCall("debounce " + commandType,
                    gui -> runOnUiThread(this::updateComodinesLabels));
        });
        debounceTransition.playFromStart();
    }

    // ─── CONFIGURACIÓN DEL CAMPO VEHÍCULO ─────────────────────────────
    private void configureVehiculoFieldListener() {
        // Actualización inmediata al perder foco o al presionar ENTER
        txtVehiculo.focusedProperty().addListener((obs, wasFocused, isNowFocused) -> {
            if (isUpdatingVehiculoField) return;
            if (wasFocused && !isNowFocused && searchProductGui != null) {
                updateVehiculoServer();
            }
        });
        txtVehiculo.setOnAction(event -> {
            if (searchProductGui != null) {
                updateVehiculoServer();
            }
        });
        // NUEVA lógica: debounce para txtVehiculo al teclear
        txtVehiculo.textProperty().addListener((obs, oldText, newText) -> {
            vehiculoDebounceTransition.stop();
            vehiculoDebounceTransition.setOnFinished(e -> updateVehiculoServer());
            vehiculoDebounceTransition.playFromStart();
        });
    }

    /**
     * Envía al servidor el nuevo valor del campo vehiculo si ha cambiado.
     */
    private void updateVehiculoServer() {
        String newVehiculo = txtVehiculo.getText();
        String normalizedNew = (newVehiculo != null && !newVehiculo.trim().isEmpty()) ? newVehiculo.trim() : null;
        String currentVehiculo = searchProductGui.getVehiculoSearch();
        String normalizedOld = (currentVehiculo != null && !currentVehiculo.trim().isEmpty()) ? currentVehiculo.trim() : null;
        if (!Objects.equals(normalizedNew, normalizedOld)) {
            subscribeOnBoundedElastic(
                    searchProductGuiService.updateVehiculoSearch(searchProductGui.getId(), normalizedNew),
                    updatedGui -> {
                        log.info("VehiculoSearch actualizado: {}", normalizedNew);
                        runOnUiThread(() -> txtVehiculo.positionCaret(txtVehiculo.getText().length()));
                    },
                    logError("actualizando vehiculoSearch")
            );
        }
    }

    // ─── CONFIRMACIÓN Y CANCELACIÓN DEL MODO COMANDO ─────────────────────────────
    private void confirmCommand(String inputText) {
        if (inputText.isEmpty()) {
            cancelCommand();
            return;
        }
        switch (currentCommandType) {
            case DESCRIPCION -> searchProductGui.setDescripcion(inputText);
            case CODIGO_PRODUCTO -> searchProductGui.setCodProductoOld(inputText);
            case CODIGO_FABRICA -> searchProductGui.setCodFabricaOld(inputText);
        }
        updateComodinesServiceCall("confirmCommand", updatedGui -> {
            if (currentCommandType == CommandType.CODIGO_FABRICA &&
                    (updatedGui.getCodFabricaOld() == null || updatedGui.getCodFabricaOld().isEmpty())) {
                updatedGui.setCodFabricaOld(inputText);
            }
            setSearchProductGui(updatedGui);
            log.info("Comando confirmado: {}, valor='{}'.", currentCommandType, inputText);
            resetCommandMode();
        });
    }

    private void cancelCommand() {
        resetCommandMode();
    }

    private void resetCommandMode() {
        runOnUiThread(() -> {
            txtSearch.setLeft(null);
            txtSearch.clear();
        });
        isCommandMode = false;
        currentCommandType = null;
    }

    // ─── ACTUALIZACIÓN DE COMODINES ─────────────────────────────
    private void updateComodinesServiceCall(String action, Consumer<SearchProductGui> onSuccess) {
        log.debug("Enviando actualización de comodines ({}) - descripcion: '{}', codProductoOld: '{}', codFabricaOld: '{}'",
                action,
                searchProductGui.getDescripcion(),
                searchProductGui.getCodProductoOld(),
                searchProductGui.getCodFabricaOld());

        subscribeOnBoundedElastic(
                searchProductGuiService.updateComodines(searchProductGui),
                updatedGui -> {
                    log.debug("Comodines actualizados ({}) - descripcion: '{}', codProductoOld: '{}', codFabricaOld: '{}'",
                            action,
                            updatedGui.getDescripcion(),
                            updatedGui.getCodProductoOld(),
                            updatedGui.getCodFabricaOld());
                    onSuccess.accept(updatedGui);

                    // Forzar recarga de productos si hay un controlador padre
                    if (parentController != null) {
                        log.info("Forzando recarga de productos desde SearchFilterController");
                        parentController.forceProductReload();
                    } else {
                        log.warn("No se puede forzar recarga de productos: parentController es nulo");
                    }
                },
                logError("actualizando comodines (" + action + ")")
        );
    }

    /**
     * Actualiza la sección de comodines en la UI.
     */
    private void updateComodinesLabels() {
        runOnUiThread(() -> {
            List<Node> newLabels = new ArrayList<>();
            Map<String, java.util.function.Supplier<String>> fieldGetters = Map.of(
                    "Descripción", () -> searchProductGui.getDescripcion(),
                    "Código de Producto", () -> searchProductGui.getCodProductoOld(),
                    "Código de Fábrica", () -> searchProductGui.getCodFabricaOld()
            );
            Map<String, Runnable> clearActions = Map.of(
                    "Descripción", () -> updateFieldAndRefresh(() -> searchProductGui.setDescripcion(null), "limpiando Descripción"),
                    "Código de Producto", () -> updateFieldAndRefresh(() -> searchProductGui.setCodProductoOld(null), "limpiando Código de Producto"),
                    "Código de Fábrica", () -> updateFieldAndRefresh(() -> searchProductGui.setCodFabricaOld(null), "limpiando Código de Fábrica")
            );
            fieldGetters.forEach((label, getter) -> {
                String value = getter.get();
                if (value != null && !value.trim().isEmpty()) {
                    HBox labelBox = FXLabelUtil.createStaticCommandLabel(label, value, clearActions.get(label));
                    HBox.setHgrow(labelBox, Priority.ALWAYS);
                    newLabels.add(labelBox);
                }
            });
            comodines.setSpacing(3);
            comodines.getChildren().setAll(newLabels);
        });
    }

    private void updateFieldAndRefresh(Runnable clearFieldAction, String actionDescription) {
        clearFieldAction.run();
        updateComodinesServiceCall(actionDescription, gui -> runOnUiThread(this::updateComodinesLabels));
    }

    // ─── ACTUALIZACIÓN DEL GRUPO ─────────────────────────────
    private void updateGroupLabels() {
        runOnUiThread(() -> {
            String currentGroupId = searchProductGui.getIdGrupo();
            if (currentGroupId == null) {
                if (lastGroupId != null) {
                    grupos.getChildren().clear();
                    lastGroupId = null;
                }
                return;
            }
            if (currentGroupId.equals(lastGroupId)) return;
            subscribeOnBoundedElastic(
                    grupoService.getGrupoById(currentGroupId),
                    grupo -> {
                        String nombreGrupo = grupo.getNombrePrincipal();
                        lastGroupId = currentGroupId;
                        runOnUiThread(() -> {
                            grupos.getChildren().clear();
                            Label groupLabel = new Label(nombreGrupo);
                            groupLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold; -fx-text-fill: -fx-light-grey;");
                            FontIcon closeIcon = new FontIcon("fas-times");
                            closeIcon.setIconSize(12);
                            closeIcon.getStyleClass().add("font-icon-light");
                            Label closeLabel = new Label();
                            closeLabel.setGraphic(closeIcon);
                            closeLabel.setStyle("-fx-cursor: hand;");
                            closeLabel.setOnMouseClicked(e ->
                                    subscribeOnBoundedElastic(
                                            searchProductGuiService.updateGrupo(searchProductGui.getId(), null),
                                            updatedGui -> log.info("Grupo eliminado"),
                                            logError("eliminar grupo")
                                    )
                            );
                            HBox groupBox = new HBox(5, groupLabel, closeLabel);
                            groupBox.setAlignment(Pos.CENTER_LEFT);
                            grupos.getChildren().add(groupBox);
                        });
                    },
                    logError("obtener Grupo")
            );
        });
    }

    // ─── ACTUALIZACIÓN DE FILTROS ─────────────────────────────
    private void updateFilters() {
        runOnUiThread(() -> {
            List<FiltroDatoRellenado> filters = searchProductGui.getFiltroDatoRellenados();
            if (filters == null || filters.isEmpty()) {
                grupoFiltros.getChildren().clear();
                filterControllerMap.clear();
                filterParentMap.clear();
                if (scrollPaneGrupoFiltros != null) {
                    scrollPaneGrupoFiltros.setVisible(false);
                    scrollPaneGrupoFiltros.setManaged(false);
                }
                return;
            } else if (scrollPaneGrupoFiltros != null) {
                scrollPaneGrupoFiltros.setVisible(true);
                scrollPaneGrupoFiltros.setManaged(true);
            }
            filters.sort(Comparator.comparing(FiltroDatoRellenado::getFila)
                    .thenComparing(FiltroDatoRellenado::getColumna));
            Map<Integer, HBox> rowMap = new TreeMap<>();
            for (FiltroDatoRellenado filterData : filters) {
                if (filterData.getFiltro() == null ||
                        filterData.getFiltro().getTipo() == null ||
                        "CADENA_TEXTO".equals(filterData.getFiltro().getTipo().name())) {
                    String key = filterData.getFila() + ":" + filterData.getColumna();
                    FilterStringController controller;
                    Parent filterComponent;
                    if (filterControllerMap.containsKey(key)) {
                        controller = filterControllerMap.get(key);
                        filterComponent = filterParentMap.get(key);
                    } else {
                        filterComponent = springFXMLLoader.load("fxml/sale/searchproduct/filter/filterString.fxml");
                        controller = springFXMLLoader.getController(filterComponent);
                        filterControllerMap.put(key, controller);
                        filterParentMap.put(key, filterComponent);
                    }
                    controller.setFilterData(filterData, searchProductGui);
                    HBox.setHgrow(filterComponent, Priority.ALWAYS);
                    int row = filterData.getFila();
                    HBox rowBox = rowMap.getOrDefault(row, new HBox(10));
                    rowBox.getChildren().add(filterComponent);
                    rowMap.put(row, rowBox);
                }
            }
            grupoFiltros.getChildren().clear();
            rowMap.values().forEach(rowBox -> {
                // Aseguramos que cada fila tenga un estilo que indique que es clickeable
                rowBox.setStyle("-fx-padding: 5;");

                // Añadimos un icono de opciones al final de cada fila para facilitar el acceso al menú contextual
                FontIcon optionsIcon = new FontIcon("fas-ellipsis-v");
                optionsIcon.setIconSize(18);
                optionsIcon.getStyleClass().add("font-icon-light");

                // Creamos un contenedor para el icono con un padding para hacerlo más fácil de clickear
                Label optionsLabel = new Label();
                optionsLabel.setGraphic(optionsIcon);
                optionsLabel.setStyle("-fx-cursor: hand; -fx-padding: 8 12; -fx-background-color: rgba(200, 200, 200, 0.15); -fx-background-radius: 4;");

                // Centramos el icono verticalmente
                optionsLabel.setAlignment(javafx.geometry.Pos.CENTER);
                HBox.setMargin(optionsLabel, new javafx.geometry.Insets(0, 0, 0, 5));

                // Añadimos un efecto hover para mejorar la experiencia de usuario
                optionsLabel.setOnMouseEntered(e -> optionsLabel.setStyle("-fx-cursor: hand; -fx-padding: 8 12; -fx-background-color: rgba(200, 200, 200, 0.4); -fx-background-radius: 4;"));
                optionsLabel.setOnMouseExited(e -> optionsLabel.setStyle("-fx-cursor: hand; -fx-padding: 8 12; -fx-background-color: rgba(200, 200, 200, 0.15); -fx-background-radius: 4;"));

                // Añadimos el icono al final de la fila
                rowBox.getChildren().add(optionsLabel);

                // Configuramos el evento de clic derecho específicamente para este icono
                configureRowOptionsMenu(optionsLabel, rowBox);

                grupoFiltros.getChildren().add(rowBox);
            });
        });
    }

    // ─── HABILITAR/DESHABILITAR CONTROLES ─────────────────────────────
    public void enableControls(boolean enabled) {
        txtSearch.setDisable(!enabled);
        txtVehiculo.setDisable(!enabled);
    }

    // ─── MÉTODO PARA ACTUALIZAR TODA LA UI ─────────────────────────────
    public void setSearchProductGui(SearchProductGui spGui) {
        this.searchProductGui = spGui;
        updateGroupLabels();
        updateVehiculoField();
        updateFilters();
        updateComodinesLabels();
    }

    // ─── ACTUALIZACIÓN DEL CAMPO VEHÍCULO EN LA UI ─────────────────────────────
    private void updateVehiculoField() {
        runOnUiThread(() -> {
            String currentVehiculo = Optional.ofNullable(searchProductGui.getVehiculoSearch()).orElse("");
            if (currentVehiculo.equals(lastVehiculo)) return;
            lastVehiculo = currentVehiculo;
            isUpdatingVehiculoField = true;
            txtVehiculo.setText(currentVehiculo);
            isUpdatingVehiculoField = false;
        });
    }

    // ─── CONFIGURACIÓN DEL MENÚ CONTEXTUAL PARA DUPLICAR FILAS DE FILTROS ─────────────────────────────
    private void configureFilterRowContextMenu() {
        // Eliminamos la funcionalidad de clic derecho en cualquier parte de la fila
        // Ahora solo se usará el icono de opciones al final de cada fila
        // No es necesario hacer nada aquí, ya que la configuración se hace en configureRowOptionsMenu
    }

    /**
     * Configura el menú contextual para el icono de opciones de una fila.
     *
     * @param optionsLabel El label que contiene el icono de opciones
     * @param rowBox La fila (HBox) a la que pertenece el icono
     */
    private void configureRowOptionsMenu(Label optionsLabel, HBox rowBox) {
        // Configuramos eventos para cualquier tipo de clic en el icono
        optionsLabel.setOnMouseClicked(event -> {
            // Creamos un menú contextual para esta fila específica
            ContextMenu contextMenu = createRowContextMenu(rowBox);

            // Mostramos el menú contextual junto al icono, ligeramente desplazado para mejor visibilidad
            contextMenu.show(optionsLabel, event.getScreenX(), event.getScreenY() + 5);

            // Consumimos el evento para evitar que se propague
            event.consume();
        });

        // Añadimos un tooltip para indicar la funcionalidad
        optionsLabel.setTooltip(new Tooltip("Opciones de fila"));
    }

    /**
     * Crea un menú contextual para una fila específica.
     *
     * @param rowBox La fila (HBox) para la que se creará el menú
     * @return El menú contextual configurado
     */
    private ContextMenu createRowContextMenu(HBox rowBox) {
        ContextMenu contextMenu = new ContextMenu();

        // Opción para duplicar la fila
        MenuItem duplicateMenuItem = new MenuItem("Duplicar fila");
        FontIcon duplicateIcon = new FontIcon("fas-copy");
        duplicateIcon.setIconSize(14);
        duplicateIcon.getStyleClass().add("font-icon-light");
        duplicateMenuItem.setGraphic(duplicateIcon);

        // Aplicamos estilo al menú para hacerlo más visible
        duplicateMenuItem.setStyle("-fx-padding: 5 10;");

        // Configuramos la acción para duplicar la fila
        duplicateMenuItem.setOnAction(e -> {
            int visualRowIndex = grupoFiltros.getChildren().indexOf(rowBox);
            if (visualRowIndex >= 0) {
                // Obtenemos el valor real de la fila (no el índice visual)
                Optional<Integer> filaRealOpt = obtenerFilaReal(visualRowIndex);

                if (filaRealOpt.isPresent()) {
                    // Usamos el valor real de la fila, no el índice visual
                    Integer filaReal = filaRealOpt.get();
                    log.info("Duplicando fila real {} (visual {})", filaReal, visualRowIndex);
                    duplicateFilterRow(filaReal);
                } else {
                    log.warn("No se pudo encontrar el valor real de la fila para el índice visual {}", visualRowIndex);
                }

                // Cerramos el menú después de seleccionar la opción
                contextMenu.hide();
            }
        });

        // Opción para eliminar la fila
        MenuItem deleteMenuItem = new MenuItem("Eliminar fila");
        FontIcon deleteIcon = new FontIcon("fas-trash-alt");
        deleteIcon.setIconSize(14);
        deleteIcon.getStyleClass().add("font-icon-light");
        deleteMenuItem.setGraphic(deleteIcon);

        // Aplicamos estilo al menú para hacerlo más visible
        deleteMenuItem.setStyle("-fx-padding: 5 10;");

        // Configuramos la acción para eliminar la fila
        deleteMenuItem.setOnAction(e -> {
            int visualRowIndex = grupoFiltros.getChildren().indexOf(rowBox);
            if (visualRowIndex >= 0) {
                // Verificamos si hay más de una fila antes de intentar eliminar
                List<FiltroDatoRellenado> filters = searchProductGui.getFiltroDatoRellenados();
                Set<Integer> filasDistintas = filters.stream()
                        .map(FiltroDatoRellenado::getFila)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (filasDistintas.size() <= 1) {
                    // Mostramos un mensaje al usuario indicando que no se puede eliminar la única fila
                    Alert alert = new Alert(Alert.AlertType.WARNING);
                    alert.setTitle("No se puede eliminar");
                    alert.setHeaderText("No se puede eliminar la única fila de filtros");
                    alert.setContentText("Debe haber al menos una fila de filtros.");
                    alert.showAndWait();
                } else {
                    // Obtenemos el valor real de la fila (no el índice visual)
                    Optional<Integer> filaRealOpt = obtenerFilaReal(visualRowIndex);

                    if (filaRealOpt.isPresent()) {
                        // Usamos el valor real de la fila, no el índice visual
                        Integer filaReal = filaRealOpt.get();
                        log.info("Eliminando fila real {} (visual {})", filaReal, visualRowIndex);
                        deleteFilterRow(filaReal);
                    } else {
                        log.warn("No se pudo encontrar el valor real de la fila para el índice visual {}", visualRowIndex);
                    }
                }
                // Cerramos el menú después de seleccionar la opción
                contextMenu.hide();
            }
        });

        contextMenu.getItems().addAll(duplicateMenuItem, deleteMenuItem);
        return contextMenu;
    }

    /**
     * Busca recursivamente el HBox padre que representa una fila de filtros.
     *
     * @param node El nodo donde se hizo clic
     * @return El HBox que contiene la fila de filtros, o null si no se encuentra
     */
    private HBox findRowBox(Node node) {
        if (node == null) {
            return null;
        }

        if (node instanceof HBox && node.getParent() == grupoFiltros) {
            return (HBox) node;
        }

        return findRowBox(node.getParent());
    }

    /**
     * Duplica una fila de filtros llamando al servicio correspondiente.
     *
     * @param rowIndex Índice de la fila a duplicar
     */
    private void duplicateFilterRow(int rowIndex) {
        if (searchProductGui == null || searchProductGui.getId() == null) {
            log.warn("No se puede duplicar la fila: SearchProductGui es nulo o no tiene ID");
            return;
        }

        // Obtenemos los FiltroDatoRellenado de la fila que vamos a duplicar para mostrar información
        List<FiltroDatoRellenado> filters = searchProductGui.getFiltroDatoRellenados();
        if (filters != null) {
            // Filtramos los filtros que pertenecen a la fila seleccionada
            List<FiltroDatoRellenado> rowFilters = filters.stream()
                    .filter(f -> f.getFila() != null && f.getFila() == rowIndex)
                    .toList();

            if (!rowFilters.isEmpty()) {
                log.info("Duplicando fila {} con {} filtros", rowIndex, rowFilters.size());
            }
        }

        log.info("Duplicando fila de filtros con índice: {}", rowIndex);
        subscribeOnBoundedElastic(
                searchProductGuiService.duplicateFiltroGroupRow(searchProductGui.getId(), rowIndex),
                updatedGui -> {
                    log.info("Fila de filtros duplicada correctamente");
                    // La UI se actualizará automáticamente a través de la suscripción a SearchProductGui
                },
                error -> {
                    log.error("Error al duplicar fila de filtros: {}", error.getMessage(), error);
                }
        );
    }

    /**
     * Obtiene el valor real de la fila a partir del índice visual.
     *
     * @param visualRowIndex Índice visual de la fila en la interfaz
     * @return Optional con el valor real de la fila, o empty si no se encuentra
     */
    private Optional<Integer> obtenerFilaReal(int visualRowIndex) {
        if (searchProductGui == null || searchProductGui.getFiltroDatoRellenados() == null) {
            return Optional.empty();
        }

        List<FiltroDatoRellenado> filters = searchProductGui.getFiltroDatoRellenados();

        // Ordenamos los filtros por fila y columna
        List<FiltroDatoRellenado> filtrosOrdenados = new ArrayList<>(filters);
        filtrosOrdenados.sort(Comparator.comparing(FiltroDatoRellenado::getFila)
                .thenComparing(FiltroDatoRellenado::getColumna));

        // Agrupamos por fila
        Map<Integer, List<FiltroDatoRellenado>> filasFiltros = filtrosOrdenados.stream()
                .filter(filtro -> filtro.getFila() != null)
                .collect(Collectors.groupingBy(FiltroDatoRellenado::getFila));

        // Convertimos a lista ordenada de filas distintas
        List<Integer> filasOrdenadas = new ArrayList<>(filasFiltros.keySet());
        Collections.sort(filasOrdenadas);

        // Si el índice visual está dentro del rango de filas distintas
        if (visualRowIndex >= 0 && visualRowIndex < filasOrdenadas.size()) {
            // Obtenemos el valor real de la fila
            return Optional.of(filasOrdenadas.get(visualRowIndex));
        }

        return Optional.empty();
    }

    /**
     * Elimina una fila de filtros llamando al servicio correspondiente.
     * Solo se puede eliminar si hay más de una fila de filtros.
     *
     * @param rowIndex Índice de la fila a eliminar
     */
    private void deleteFilterRow(int rowIndex) {
        if (searchProductGui == null || searchProductGui.getId() == null) {
            log.warn("No se puede eliminar la fila: SearchProductGui es nulo o no tiene ID");
            return;
        }

        // Obtenemos los FiltroDatoRellenado para verificar si hay más de una fila
        List<FiltroDatoRellenado> filters = searchProductGui.getFiltroDatoRellenados();
        if (filters == null || filters.isEmpty()) {
            log.warn("No hay filtros para eliminar");
            return;
        }

        // Contamos las filas distintas
        Set<Integer> filasDistintas = filters.stream()
                .map(FiltroDatoRellenado::getFila)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (filasDistintas.size() <= 1) {
            log.warn("No se puede eliminar la única fila de filtros");
            // Podemos mostrar un mensaje al usuario aquí si lo deseamos
            return;
        }

        log.info("Eliminando fila de filtros con índice: {}", rowIndex);
        subscribeOnBoundedElastic(
                searchProductGuiService.deleteFiltroGroupRow(searchProductGui.getId(), rowIndex),
                updatedGui -> {
                    log.info("Fila de filtros eliminada correctamente");
                    // La UI se actualizará automáticamente a través de la suscripción a SearchProductGui
                },
                error -> {
                    log.error("Error al eliminar fila de filtros: {}", error.getMessage(), error);
                }
        );
    }
}
