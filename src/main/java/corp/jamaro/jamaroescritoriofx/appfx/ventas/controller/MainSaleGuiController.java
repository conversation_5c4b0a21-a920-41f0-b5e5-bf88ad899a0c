package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.MainSaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.ToSaleGuiRelationDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.MainSaleGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import javafx.animation.PauseTransition;
import javafx.collections.ListChangeListener;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.util.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class MainSaleGuiController extends BaseController {

    private final MainSaleGuiService mainSaleGuiService;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;

    @FXML
    private AnchorPane apMainSale;
    @FXML
    private Button btnAddSale;
    @FXML
    private ProgressIndicator loadingIndicator;
    @FXML
    private StackPane spRootMainSale;
    @FXML
    private TabPane tpSales;

    // Identificador del MainSaleGuiDto
    private UUID mainSaleGuiId;
    // Estado actual recibido desde el servidor (DTO)
    private MainSaleGuiDto currentMainSaleGui;
    // Variables para manejo de reordenamiento (se mantiene para la lógica de reorder)

    private boolean ignoringTabChanges = false;
    private List<UUID> oldTabIds = new ArrayList<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private ScheduledFuture<?> reorderTask;
    private final AtomicReference<Tab> lastReorderedTab = new AtomicReference<>();
    private final AtomicReference<Integer> lastReorderedIndex = new AtomicReference<>();
    // Bandera para indicar que se espera un nuevo tab
    private boolean pendingNewTab = false;

    // PauseTransition para debouncing de la acción de agregar SaleGui (400ms)
    private final PauseTransition addSaleDebounceTransition = new PauseTransition(Duration.millis(400));

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        tpSales.setTabDragPolicy(TabPane.TabDragPolicy.REORDER);
        tpSales.getTabs().addListener(this::handleTabsChange);
        // En vez de llamar directamente a handleAddSale(), se llama al método debouncedAddSale()
        btnAddSale.setOnAction(e -> debouncedAddSale());

        // Configuramos el listener para que al presionar F1 se invoque la acción con debounce
        spRootMainSale.setOnKeyPressed(this::handleKeyPressed);
        spRootMainSale.requestFocus();
    }

    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.F1) {
            debouncedAddSale();
            event.consume();
        }
    }

    /**
     * Método que utiliza un PauseTransition para hacer debounce de la acción de agregar SaleGui.
     */
    private void debouncedAddSale() {
        addSaleDebounceTransition.stop();
        addSaleDebounceTransition.setOnFinished(e -> handleAddSale());
        addSaleDebounceTransition.playFromStart();
    }

    /**
     * Configura el ID del MainSaleGuiDto y se suscribe a los cambios.
     */
    public void setMainSaleGuiId(UUID id) {
        this.mainSaleGuiId = id;
        log.info("[CLIENT] setMainSaleGuiId => {}", id);
        Disposable subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                mainSaleGuiService.subscribeToChanges(id),
                this::onMainSaleGuiUpdate,
                this::handleSubscriptionError,
                () -> log.debug("[CLIENT] Suscripción a MainSaleGuiDto completada.")
        );
        registerSubscription(subscription);
    }

    /**
     * Actualiza el estado actual con el DTO recibido y reconstruye la vista.
     */
    private void onMainSaleGuiUpdate(MainSaleGuiDto updated) {
        runOnUiThread(() -> {
            this.currentMainSaleGui = updated;
            log.debug("[CLIENT] Recibido MainSaleGuiDto: {}", updated);
            rebuildTabsFromServer();
        });
    }

    /**
     * Actualiza los tabs basándose en el conjunto de ToSaleGuiRelationDto ordenado por ordenPresentacion.
     * Optimizado para minimizar la recreación de tabs y componentes.
     */
    private void rebuildTabsFromServer() {
        if (currentMainSaleGui == null) return;
        ignoringTabChanges = true;
        int previousCount = tpSales.getTabs().size();

        // Ordenar las relaciones por ordenPresentacion
        List<ToSaleGuiRelationDto> sorted = new ArrayList<>(currentMainSaleGui.getSalesGui());
        sorted.sort(Comparator.comparing(
                ToSaleGuiRelationDto::getOrdenPresentacion,
                Comparator.nullsFirst(Comparator.naturalOrder())
        ));

        // Asignar órdenes a relaciones con orden nulo
        for (ToSaleGuiRelationDto rel : sorted) {
            if (rel.getOrdenPresentacion() == null) {
                log.warn("[CLIENT] Orden nulo detectado para SaleGui id={}. Asignando fallback.", rel.getSaleGuiId());
                rel.setOrdenPresentacion(sorted.indexOf(rel) + 1);
            }
        }

        // Obtener IDs válidos del servidor
        Set<UUID> validIds = sorted.stream()
                .map(ToSaleGuiRelationDto::getSaleGuiId)
                .collect(Collectors.toSet());

        // Crear un mapa de los tabs actuales para acceso rápido
        Map<UUID, Tab> currentTabs = new HashMap<>();
        for (Tab tab : tpSales.getTabs()) {
            ToSaleGuiRelationDto rel = (ToSaleGuiRelationDto) tab.getUserData();
            if (rel != null) {
                currentTabs.put(rel.getSaleGuiId(), tab);
            }
        }

        // Verificar si hay cambios en el conjunto de tabs o en su orden
        boolean tabsChanged = false;
        List<UUID> currentIds = new ArrayList<>(currentTabs.keySet());
        List<UUID> newIds = new ArrayList<>(validIds);

        // Si los conjuntos de IDs son diferentes, definitivamente hay cambios
        if (!new HashSet<>(currentIds).equals(new HashSet<>(newIds))) {
            tabsChanged = true;
            log.debug("[CLIENT] Conjunto de tabs cambiado: {} -> {}", currentIds, newIds);
        } else {
            // Verificar si el orden ha cambiado
            List<UUID> sortedIds = sorted.stream()
                    .map(ToSaleGuiRelationDto::getSaleGuiId)
                    .collect(Collectors.toList());

            if (!currentIds.equals(sortedIds)) {
                tabsChanged = true;
                log.debug("[CLIENT] Orden de tabs cambiado: {} -> {}", currentIds, sortedIds);
            }
        }

        // Si no hay cambios en los tabs ni en su orden, solo actualizamos los datos
        if (!tabsChanged) {
            log.debug("[CLIENT] No hay cambios en tabs ni en su orden, solo actualizando datos");
            for (ToSaleGuiRelationDto rel : sorted) {
                Tab tab = currentTabs.get(rel.getSaleGuiId());
                if (tab != null) {
                    // Actualizar solo los datos del tab
                    tab.setUserData(rel);
                    int orden = (rel.getOrdenPresentacion() != null) ? rel.getOrdenPresentacion() : (sorted.indexOf(rel) + 1);
                    tab.setText("Venta #" + orden);
                }
            }
        } else {
            // Hay cambios, necesitamos reorganizar los tabs
            log.debug("[CLIENT] Reorganizando tabs debido a cambios");

            // Eliminar tabs que ya no son válidos
            tpSales.getTabs().removeIf(tab -> {
                var rel = (ToSaleGuiRelationDto) tab.getUserData();
                return rel != null && !validIds.contains(rel.getSaleGuiId());
            });

            // Crear lista con el nuevo orden
            List<Tab> newOrder = new ArrayList<>();
            for (ToSaleGuiRelationDto rel : sorted) {
                Tab tab = currentTabs.get(rel.getSaleGuiId());
                if (tab == null) {
                    // Crear nuevo tab si no existe
                    tab = createTabForRelation(rel);
                    log.debug("[CLIENT] Creando nuevo tab para SaleGui id={}", rel.getSaleGuiId());
                } else {
                    // Actualizar datos del tab existente
                    tab.setUserData(rel);
                    int orden = (rel.getOrdenPresentacion() != null) ? rel.getOrdenPresentacion() : (sorted.indexOf(rel) + 1);
                    tab.setText("Venta #" + orden);
                }
                newOrder.add(tab);
            }

            // Actualizar el orden de los tabs
            tpSales.getTabs().setAll(newOrder);

            // Seleccionar el nuevo tab si corresponde
            if (pendingNewTab && newOrder.size() > previousCount) {
                Tab newTab = newOrder.get(newOrder.size() - 1);
                tpSales.getSelectionModel().select(newTab);
                pendingNewTab = false;
            }
        }

        // Actualizar la lista de IDs para comparaciones futuras
        oldTabIds = sorted.stream()
                .map(ToSaleGuiRelationDto::getSaleGuiId)
                .collect(Collectors.toList());

        ignoringTabChanges = false;
    }

    /**
     * Busca un Tab cuyo userData (ToSaleGuiRelationDto) tenga saleGuiId igual al indicado.
     */
    private Tab findTabBySaleGuiId(UUID saleGuiId) {
        for (Tab t : tpSales.getTabs()) {
            var rel = (ToSaleGuiRelationDto) t.getUserData();
            if (rel != null && rel.getSaleGuiId().equals(saleGuiId)) {
                return t;
            }
        }
        return null;
    }

    /**
     * Crea un nuevo Tab para la relación, pasando el objeto ToSaleGuiRelationDto al SaleGuiController.
     */
    private Tab createTabForRelation(ToSaleGuiRelationDto rel) {
        Tab tab = new Tab("Venta #" + rel.getOrdenPresentacion());
        tab.setClosable(true);
        tab.setUserData(rel);
        Parent saleView = springFXMLLoader.load(FXMLEnum.SALE_GUI);
        SaleGuiController ctrl = springFXMLLoader.getController(saleView);
        ctrl.setToSaleGuiRelation(rel);
        tab.setContent(saleView);
        tab.setOnCloseRequest(evt -> removeSaleFromServer(rel.getSaleGuiId(), evt::consume));
        return tab;
    }

    private void handleTabsChange(ListChangeListener.Change<? extends Tab> change) {
        if (ignoringTabChanges) return;
        List<UUID> beforeIds = new ArrayList<>(oldTabIds);
        while (change.next()) {
            log.debug("[CLIENT] TabChange => wasPermutated={}, removed={}, added={}",
                    change.wasPermutated(), change.getRemoved(), change.getAddedSubList());
        }
        List<UUID> afterIds = tpSales.getTabs().stream()
                .map(t -> ((ToSaleGuiRelationDto) t.getUserData()).getSaleGuiId())
                .collect(Collectors.toList());
        oldTabIds = afterIds;
        if (beforeIds.size() != afterIds.size()) return;
        if (!sameElements(beforeIds, afterIds)) return;
        if (!Objects.equals(beforeIds, afterIds)) {
            log.info("[CLIENT] Detectado reorder => antes={} | después={}", beforeIds, afterIds);
            detectLargestMove(beforeIds, afterIds);
            scheduleReorderDebounce();
        }
    }

    private boolean sameElements(List<UUID> a, List<UUID> b) {
        if (a.size() != b.size()) return false;
        var sa = new ArrayList<>(a);
        var sb = new ArrayList<>(b);
        Collections.sort(sa);
        Collections.sort(sb);
        return sa.equals(sb);
    }

    private void detectLargestMove(List<UUID> beforeIds, List<UUID> afterIds) {
        int largestDelta = 0;
        Tab tabWithLargestDelta = null;
        int finalIndex = -1;
        for (int i = 0; i < afterIds.size(); i++) {
            UUID newId = afterIds.get(i);
            int oldIndex = beforeIds.indexOf(newId);
            if (oldIndex != i) {
                int delta = Math.abs(oldIndex - i);
                if (delta > largestDelta) {
                    largestDelta = delta;
                    finalIndex = i;
                    tabWithLargestDelta = tpSales.getTabs().get(i);
                }
            }
        }
        if (tabWithLargestDelta != null && finalIndex != -1) {
            lastReorderedTab.set(tabWithLargestDelta);
            lastReorderedIndex.set(finalIndex);
        }
    }

    private void scheduleReorderDebounce() {
        if (reorderTask != null) {
            reorderTask.cancel(false);
        }
        reorderTask = scheduler.schedule(this::executeReorder, 200, TimeUnit.MILLISECONDS);
    }

    private void executeReorder() {
        Tab tab = lastReorderedTab.getAndSet(null);
        Integer finalIndex = lastReorderedIndex.getAndSet(null);
        if (tab == null || finalIndex == null) return;
        runOnUiThread(() -> {
            var rel = (ToSaleGuiRelationDto) tab.getUserData();
            if (rel == null) return;
            int realIndex = tpSales.getTabs().indexOf(tab);
            if (realIndex < 0) return;
            int finalPos = realIndex + 1;
            int oldPos = rel.getOrdenPresentacion();
            if (finalPos == oldPos) {
                log.debug("[CLIENT] No hubo cambio real de posición; se ignora reorder.");
                return;
            }
            UUID saleGuiId = rel.getSaleGuiId();
            log.info("[CLIENT] Reordenando SaleGui {} => newPos={}", saleGuiId, finalPos);
            mainSaleGuiService.reorderSaleGui(mainSaleGuiId, saleGuiId, finalPos)
                    .subscribe(
                            unused -> log.debug("[CLIENT] Reordenamiento en servidor OK"),
                            error -> runOnUiThread(() -> {
                                alertUtil.showError("No se pudo reordenar: " + error.getMessage());
                                rebuildTabsFromServer();
                            })
                    );
        });
    }

    private void removeSaleFromServer(UUID saleGuiId, Runnable onErrorCancelClose) {
        if (currentMainSaleGui == null) {
            onErrorCancelClose.run();
            return;
        }
        mainSaleGuiService.removeSaleGui(mainSaleGuiId, saleGuiId)
                .subscribe(
                        unused -> log.debug("[CLIENT] SaleGui {} removido OK", saleGuiId),
                        error -> runOnUiThread(() -> {
                            alertUtil.showError("No se pudo remover la venta: " + error.getMessage());
                            onErrorCancelClose.run();
                        })
                );
    }

    /**
     * Maneja la acción de agregar un nuevo SaleGui.
     * Se activa la bandera pendingNewTab para que, al recibir la actualización del servidor,
     * se seleccione automáticamente el nuevo tab.
     * (Este método se invoca mediante el debounce implementado en debouncedAddSale())
     */
    private void handleAddSale() {
        if (mainSaleGuiId == null) {
            alertUtil.showError("No hay mainSaleGuiId configurado.");
            return;
        }
        pendingNewTab = true;
        subscribeOnBoundedElastic(
                mainSaleGuiService.createNewSaleGui(mainSaleGuiId),
                unused -> log.debug("[CLIENT] Nuevo SaleGui creado OK"),
                error -> runOnUiThread(() -> alertUtil.showError("No se pudo agregar la venta: " + error.getMessage()))
        );
    }

    private void handleSubscriptionError(Throwable error) {
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de MainSaleGui: " + error.getMessage()));
        log.error("[CLIENT] Error en la suscripción de MainSaleGuiDto: {}", error.getMessage(), error);
    }

    @Override
    public void onClose() {
        super.onClose();
        if (!scheduler.isShutdown()) {
            scheduler.shutdownNow();
        }
        log.info("[CLIENT] MainSaleGuiController cerrado.");
    }
}
