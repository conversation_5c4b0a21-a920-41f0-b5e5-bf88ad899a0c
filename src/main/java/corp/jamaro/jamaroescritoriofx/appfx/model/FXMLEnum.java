package corp.jamaro.jamaroescritoriofx.appfx.model;

import lombok.Getter;

/**
 * Enumeración de las vistas FXML disponibles en la aplicación.
 * Cada valor enum contiene la ruta al archivo FXML correspondiente.
 */
@Getter
public enum FXMLEnum {

    LOGIN_RFID("/fxml/loginRfidView.fxml"),
    VENTAS_PRINCIPAL("/fxml/venta/ventasPrincipalView.fxml"),
    VENTA("/fxml/venta/ventaView.fxml"),
    SEARCH_PRODUCT_CATEGORY("/fxml/venta/busquedaproducto/busquedaProductoFiltros.fxml"),

    CREAR_VEHICULO("fxml/vehiculo/creacionVehiculo.fxml"),
    CREAR_VEHICULO_MARCA("fxml/vehiculo/crearVehiculoMarca.fxml"),
    CREAR_VEHICULO_MODELO("fxml/vehiculo/crearVehiculoModelo.fxml"),
    CREAR_VEHICULO_MOTOR("fxml/vehiculo/crearVehiculoMotor.fxml"),
    CREAR_VEHICULO_VERSION("fxml/vehiculo/crearVehiculoVersion.fxml"),
    BUSCAR_VEHICULO("fxml/vehiculo/buscarVehiculo.fxml"),
    AGREGAR_VEHICULO_NOMBRE("fxml/vehiculo/agregarVehiculoNombre.fxml"),

    UNIVERSAL_SALE("/fxml/sale/universalSale.fxml"),
    MAIN_SALE("/fxml/sale/mainSale.fxml"),
    SALE_GUI("/fxml/sale/saleGui.fxml"),
    SALE("/fxml/sale/sale.fxml"),
    SEARCH_PRODUCT("/fxml/sale/searchproduct/searchProductGui.fxml"),
    ;

    private final String path;

    FXMLEnum(String path) {
        this.path = path;
    }

}
//    VENTAS_PRINCIPAL("/fxml/venta/ventasPrincipalView.fxml"),
//    VENTA("/fxml/venta/ventaView.fxml"),
//    SEARCH_PRODUCT_CATEGORY("/fxml/venta/busquedaproducto/busquedaProductoFiltros.fxml"),
////    SEARCH_PRODUCT("/fxml/venta/busquedaproducto/busquedaProductoView.fxml"),
